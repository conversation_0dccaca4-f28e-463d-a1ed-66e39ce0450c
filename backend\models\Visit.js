const mongoose = require('mongoose');

const visitSchema = new mongoose.Schema({
  ip: { type: String, required: true },
  timestamp: { type: Date, default: Date.now },
  section: { type: String, required: true },
  duration: { type: Number, default: 0 }, // Duration in seconds
  userAgent: { type: String, default: '' }, // Browser/device info
  sessionId: { type: String, default: '' }, // To group visits by session
  pageUrl: { type: String, default: '' }, // Full page URL for job details tracking

  // Enhanced tracking fields for job details and project interactions
  jobTitle: { type: String, default: '' }, // Job title for job detail pages
  jobSlug: { type: String, default: '' }, // Job slug for job detail pages
  projectTitle: { type: String, default: '' }, // Project title for project interactions
  interactionType: { type: String, default: '' }, // Type of interaction (view, click, hover, etc.)
  cardTitle: { type: String, default: '' }, // Content card title
  skillCategory: { type: String, default: '' }, // Skill category for content cards

  // Project analytics specific fields
  projectType: { type: String, default: '' }, // 'experience' or 'portfolio-carousel'
  projectId: { type: String, default: '' }, // Unique identifier for the project
  projectAvailable: { type: Boolean, default: true }, // Whether project is available or shows "coming soon"
  projectUrl: { type: String, default: '' }, // URL of the project if available
});

// Index for better query performance
visitSchema.index({ ip: 1, timestamp: -1 });
visitSchema.index({ section: 1, duration: -1 });
visitSchema.index({ sessionId: 1, timestamp: -1 });
visitSchema.index({ jobSlug: 1, timestamp: -1 });
visitSchema.index({ projectTitle: 1, timestamp: -1 });
visitSchema.index({ interactionType: 1, timestamp: -1 });
visitSchema.index({ projectType: 1, timestamp: -1 });
visitSchema.index({ projectId: 1, timestamp: -1 });
visitSchema.index({ projectAvailable: 1, timestamp: -1 });

module.exports = mongoose.model('Visit', visitSchema);