/**
 * API Configuration
 * 
 * This file provides a fallback mechanism for API URL configuration
 * to ensure the frontend always has the correct backend URL.
 */

// Primary: Try to get from environment variable
let API_URL = process.env.REACT_APP_API_URL;

// Fallback: If environment variable is not set, use production URL
if (!API_URL || API_URL === 'undefined') {
  API_URL = 'https://porfolio-pro-backend.onrender.com';
}

// Export the configuration
export const API_CONFIG = {
  BASE_URL: API_URL,
  ENDPOINTS: {
    PING: `${API_URL}/api/ping`,
    ADMIN_LOGIN: `${API_URL}/api/admin/login`,
    ADMIN_DASHBOARD: `${API_URL}/api/admin/dashboard`,
    TRACK_VISIT: `${API_URL}/api/track/visit`,
    SECTION_DETAILS: `${API_URL}/api/track/section-details`,
    EXPERIENCE_PROJECTS_ANALYTICS: `${API_URL}/api/admin/experience-projects-analytics`,
    PORTFOLIO_PROJECTS_ANALYTICS: `${API_URL}/api/admin/portfolio-projects-analytics`,
  }
};

// Also export just the base URL for backward compatibility
export const API_URL_EXPORT = API_URL;

export default API_CONFIG;
