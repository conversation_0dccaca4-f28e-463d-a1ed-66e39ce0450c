import { useEffect, useRef, useCallback } from 'react';
import { logDebug, warnDebug } from '../utils/logger';
import { API_CONFIG } from '../config/apiConfig';
import { safeFetch, isExtensionError } from '../utils/extensionErrorHandler';

const API_URL = API_CONFIG.BASE_URL;

// Generate a simple session ID for grouping visits
const generateSessionId = () => {
  const existing = sessionStorage.getItem('visitor-session-id');
  if (existing) return existing;
  
  const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  sessionStorage.setItem('visitor-session-id', sessionId);
  return sessionId;
};

// Utility to format duration in readable format
export const formatDuration = (seconds) => {
  if (seconds < 60) return `${seconds}s`;
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
};

// Custom hook for tracking visitor behavior
export const useVisitorTracking = (sectionName, options = {}) => {
  const {
    threshold = 0.5, // How much of the element should be visible
    minDuration = 2, // Minimum seconds to count as a meaningful view
    trackOnMount = false, // Whether to track immediately on mount
    debounceMs = 1000 // Debounce API calls
  } = options;

  const elementRef = useRef(null);
  const observerRef = useRef(null);
  const startTimeRef = useRef(null);
  const totalDurationRef = useRef(0);
  const isVisibleRef = useRef(false);
  const debounceTimeoutRef = useRef(null);
  const sessionIdRef = useRef(generateSessionId());

  // Send tracking data to backend
  const sendTrackingData = useCallback(async (duration, pageUrl = '') => {
    if (!API_URL || duration < minDuration) return;

    try {
      await safeFetch(API_CONFIG.ENDPOINTS.TRACK_VISIT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: sectionName,
          duration: Math.round(duration),
          sessionId: sessionIdRef.current,
          pageUrl: pageUrl || window.location.href
        }),
      });

      logDebug(`📊 Tracked: ${sectionName} - ${formatDuration(Math.round(duration))}`);
    } catch (error) {
      // Don't log warnings for extension-related errors
      if (!isExtensionError(error)) {
        warnDebug('Visitor tracking failed:', error);
      }
    }
  }, [sectionName, minDuration]);

  // Debounced tracking to avoid too many API calls
  const debouncedTrack = useCallback((duration, pageUrl) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    
    debounceTimeoutRef.current = setTimeout(() => {
      sendTrackingData(duration, pageUrl);
    }, debounceMs);
  }, [sendTrackingData, debounceMs]);

  // Handle visibility changes
  const handleVisibilityChange = useCallback((isVisible) => {
    const now = Date.now();
    
    if (isVisible && !isVisibleRef.current) {
      // Element became visible
      startTimeRef.current = now;
      isVisibleRef.current = true;
    } else if (!isVisible && isVisibleRef.current) {
      // Element became hidden
      if (startTimeRef.current) {
        const sessionDuration = (now - startTimeRef.current) / 1000;
        totalDurationRef.current += sessionDuration;
        
        // Send tracking data if duration is meaningful
        if (sessionDuration >= minDuration) {
          debouncedTrack(sessionDuration);
        }
      }
      isVisibleRef.current = false;
      startTimeRef.current = null;
    }
  }, [minDuration, debouncedTrack]);

  // Initialize Intersection Observer
  useEffect(() => {
    if (!elementRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          handleVisibilityChange(entry.isIntersecting);
        });
      },
      {
        threshold,
        rootMargin: '0px 0px -10% 0px' // Trigger when element is 10% from bottom of viewport
      }
    );

    observerRef.current.observe(elementRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [threshold, handleVisibilityChange]);

  // Handle page unload to capture final duration
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (isVisibleRef.current && startTimeRef.current) {
        const finalDuration = (Date.now() - startTimeRef.current) / 1000;
        totalDurationRef.current += finalDuration;
        
        // Use sendBeacon for reliable tracking on page unload
        if (navigator.sendBeacon && totalDurationRef.current >= minDuration) {
          const data = JSON.stringify({
            section: sectionName,
            duration: Math.round(totalDurationRef.current),
            sessionId: sessionIdRef.current,
            pageUrl: window.location.href
          });
          
          navigator.sendBeacon(API_CONFIG.ENDPOINTS.TRACK_VISIT, data);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [sectionName, minDuration]);

  // Track on mount if requested
  useEffect(() => {
    if (trackOnMount) {
      sendTrackingData(0.1); // Minimal duration to indicate page load
    }
  }, [trackOnMount, sendTrackingData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      
      // Send final tracking data if there's accumulated duration
      if (totalDurationRef.current >= minDuration) {
        sendTrackingData(totalDurationRef.current);
      }
    };
  }, [sendTrackingData, minDuration]);

  return {
    ref: elementRef,
    totalDuration: totalDurationRef.current,
    isVisible: isVisibleRef.current,
    sessionId: sessionIdRef.current
  };
};

// Hook for tracking job detail pages specifically
export const useJobDetailTracking = (jobTitle, jobSlug) => {
  const sessionIdRef = useRef(generateSessionId());
  const startTimeRef = useRef(Date.now());

  useEffect(() => {
    const handleBeforeUnload = () => {
      const duration = (Date.now() - startTimeRef.current) / 1000;
      
      if (duration >= 5 && navigator.sendBeacon) { // Minimum 5 seconds
        const data = JSON.stringify({
          section: 'job-detail',
          duration: Math.round(duration),
          sessionId: sessionIdRef.current,
          pageUrl: window.location.href,
          jobTitle,
          jobSlug
        });
        
        navigator.sendBeacon(API_CONFIG.ENDPOINTS.TRACK_VISIT, data);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [jobTitle, jobSlug]);

  return {
    sessionId: sessionIdRef.current,
    startTime: startTimeRef.current
  };
};

// Hook for tracking specific project interactions
export const useProjectInteractionTracking = (jobTitle, jobSlug) => {
  const trackInteraction = useCallback(async (projectTitle, interactionType, additionalData = {}) => {
    if (!API_URL) return;

    try {
      await fetch(API_CONFIG.ENDPOINTS.TRACK_VISIT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: `project-interaction-${interactionType}`,
          duration: 1, // Minimal duration for interaction tracking
          sessionId: generateSessionId(),
          pageUrl: window.location.href,
          projectTitle,
          jobTitle,
          jobSlug,
          interactionType,
          ...additionalData
        }),
      });

      logDebug(`📊 Project Interaction: ${projectTitle} - ${interactionType}`, additionalData);
    } catch (error) {
      warnDebug('Project interaction tracking failed:', error);
    }
  }, [jobTitle, jobSlug]);

  return { trackInteraction };
};

export default useVisitorTracking;
