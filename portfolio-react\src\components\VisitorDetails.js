import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { FaArrowLeft, FaGlobe, FaClock, FaEye, FaCalendarAlt } from 'react-icons/fa';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { logSensitive, logDebug } from '../utils/logger';
import VisitorMovementSchema from './VisitorMovementSchema';
import { API_CONFIG } from '../config/apiConfig';
import { safeFetch, isExtensionError } from '../utils/extensionErrorHandler';
import './VisitorDetails.css';

const VisitorDetails = () => {
  const [visitorData, setVisitorData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { visitorIp } = useParams();

  useEffect(() => {
    const fetchVisitorDetails = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No token found. Please log in.');
        navigate('/admin/login');
        return;
      }

      // Wake up backend before fetching data
      await preemptiveWakeup();

      try {
        const response = await safeFetch(API_CONFIG.ENDPOINTS.ADMIN_DASHBOARD, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Filter visits for this specific IP
        const visitorVisits = data.visits.filter(visit => visit.ip === visitorIp);
        
        if (visitorVisits.length === 0) {
          setError('No visits found for this IP address.');
          return;
        }

        // Process visitor data
        const processedData = {
          ip: visitorIp,
          visits: visitorVisits,
          totalVisits: visitorVisits.length,
          totalDuration: visitorVisits.reduce((sum, visit) => sum + (visit.duration || 0), 0),
          firstVisit: new Date(Math.min(...visitorVisits.map(v => new Date(v.timestamp)))),
          lastVisit: new Date(Math.max(...visitorVisits.map(v => new Date(v.timestamp)))),
          sectionsVisited: [...new Set(visitorVisits.map(v => v.section))],
          sectionStats: {}
        };

        // Calculate section statistics
        processedData.sectionsVisited.forEach(section => {
          const sectionVisits = visitorVisits.filter(v => v.section === section);
          processedData.sectionStats[section] = {
            count: sectionVisits.length,
            totalDuration: sectionVisits.reduce((sum, visit) => sum + (visit.duration || 0), 0),
            avgDuration: sectionVisits.reduce((sum, visit) => sum + (visit.duration || 0), 0) / sectionVisits.length,
            lastVisit: new Date(Math.max(...sectionVisits.map(v => new Date(v.timestamp))))
          };
        });

        setVisitorData(processedData);
      } catch (error) {
        // Don't show errors for extension-related issues
        if (!isExtensionError(error)) {
          logSensitive('Visitor details fetch error:', error);
          setError('Error fetching visitor details: ' + error.message);
        } else {
          logDebug('Extension error suppressed in VisitorDetails:', error.message);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchVisitorDetails();
  }, [navigate, visitorIp]);

  const formatDuration = (seconds) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const handleBackToPreviousPage = () => {
    navigate(-1); // Navigate to previous page
  };

  if (loading) {
    return (
      <div className="visitor-details-container">
        <div className="loading-spinner">Loading visitor details...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="visitor-details-container">
        <div className="error-message">{error}</div>
        <button onClick={handleBackToPreviousPage} className="back-button">
          <FaArrowLeft /> Back to Previous Page
        </button>
      </div>
    );
  }

  return (
    <div className="visitor-details-container">
      <div className="visitor-details-header">
        <button onClick={handleBackToPreviousPage} className="back-button">
          <FaArrowLeft /> Back to Previous Page
        </button>
        <h1>Visitor Details</h1>
      </div>

      <div className="visitor-details-content">
        {/* Visitor Movement Schema */}
        <VisitorMovementSchema visitorData={visitorData} />

        {/* Visitor Overview */}
        <div className="visitor-overview-card">
          <h2><FaGlobe /> Visitor Overview</h2>
          <div className="overview-grid">
            <div className="overview-item">
              <span className="overview-label">IP Address:</span>
              <span className="overview-value">{visitorData.ip}</span>
            </div>
            <div className="overview-item">
              <span className="overview-label">Total Visits:</span>
              <span className="overview-value">{visitorData.totalVisits}</span>
            </div>
            <div className="overview-item">
              <span className="overview-label">Total Time Spent:</span>
              <span className="overview-value">{formatDuration(visitorData.totalDuration)}</span>
            </div>
            <div className="overview-item">
              <span className="overview-label">Sections Visited:</span>
              <span className="overview-value">{visitorData.sectionsVisited.length}</span>
            </div>
            <div className="overview-item">
              <span className="overview-label">First Visit:</span>
              <span className="overview-value">{visitorData.firstVisit.toLocaleString()}</span>
            </div>
            <div className="overview-item">
              <span className="overview-label">Last Visit:</span>
              <span className="overview-value">{visitorData.lastVisit.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Section Statistics */}
        <div className="section-stats-card">
          <h2><FaEye /> Section Statistics</h2>
          <div className="section-stats-grid">
            {Object.entries(visitorData.sectionStats).map(([section, stats]) => (
              <div key={section} className="section-stat-item">
                <h3>{section}</h3>
                <div className="section-stat-details">
                  <div className="stat-detail">
                    <FaEye /> <span>{stats.count} visits</span>
                  </div>
                  <div className="stat-detail">
                    <FaClock /> <span>{formatDuration(stats.totalDuration)} total</span>
                  </div>
                  <div className="stat-detail">
                    <FaClock /> <span>{formatDuration(Math.round(stats.avgDuration))} avg</span>
                  </div>
                  <div className="stat-detail">
                    <FaCalendarAlt /> <span>{stats.lastVisit.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Visit History */}
        <div className="visit-history-card">
          <h2><FaCalendarAlt /> Visit History</h2>
          <div className="visit-history-list">
            {visitorData.visits
              .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
              .map((visit, index) => (
                <div key={index} className="visit-history-item">
                  <div className="visit-timestamp">
                    {new Date(visit.timestamp).toLocaleString()}
                  </div>
                  <div className="visit-section">{visit.section}</div>
                  <div className="visit-duration">
                    {visit.duration ? formatDuration(visit.duration) : 'N/A'}
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VisitorDetails;
