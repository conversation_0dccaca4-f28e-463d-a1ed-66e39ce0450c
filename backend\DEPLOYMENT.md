# Backend Deployment Guide

## Fixing bcrypt Deployment Issues

The backend has been updated to use `bcryptjs` instead of `bcrypt` to avoid native compilation issues on deployment platforms like Render.

## Environment Variables Required

Set these environment variables in your Render dashboard:

```
MONGO_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret_key
ADMIN_EMAIL=your_admin_email
ADMIN_PASSWORD_HASH=your_hashed_password
NODE_ENV=production
PORT=5000
```

## Generating Password Hash

To generate a password hash for the ADMIN_PASSWORD_HASH environment variable:

1. Run the seed script locally:
   ```bash
   node utils/seedAdmin.js
   ```

2. Or use this Node.js script:
   ```javascript
   const bcrypt = require('bcryptjs');
   const hash = bcrypt.hashSync('your_password', 10);
   console.log(hash);
   ```

## Render Deployment Settings

- **Build Command**: `npm install`
- **Start Command**: `npm start`
- **Node.js Version**: 18.x or higher (specified in package.json)

## API Endpoints

- `POST /api/admin/login` - Admin login
- `GET /api/admin/dashboard` - Admin dashboard (requires auth)
- `POST /api/track/visit` - Track page visits

## CORS Configuration

The backend is configured to accept requests from any origin. In production, you may want to restrict this to your frontend domain. 