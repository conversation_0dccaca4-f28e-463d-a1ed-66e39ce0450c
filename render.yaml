services:
  # Backend API Service
  - type: web
    name: porfolio-pro-backend
    env: node
    rootDir: backend
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: MONGO_URI
        value: mongodb+srv://Aminos:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster69
      - key: JWT_SECRET
        value: mySuperSecretJWTKey123!
      - key: ADMIN_EMAIL
        value: <EMAIL>
      - key: ADMIN_PASSWORD
        value: Adminboss

  # Frontend React App Service
  - type: web
    name: porfolio-pro-frontend
    env: static
    rootDir: portfolio-react
    buildCommand: npm install && npm run build
    staticPublishPath: build
    envVars:
      - key: NODE_ENV
        value: production
      - key: REACT_APP_API_URL
        value: https://porfolio-pro-backend.onrender.com
