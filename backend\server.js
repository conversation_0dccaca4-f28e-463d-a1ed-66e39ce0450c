// Load environment variables with multiple strategies
require('dotenv').config();
// Also try production env file
require('dotenv').config({ path: '.env.production' });

// Manual fallback for production
if (process.env.NODE_ENV === 'production' && !process.env.MONGO_URI) {
  console.log('🔄 Attempting manual environment variable setup for production...');
  process.env.MONGO_URI = process.env.MONGO_URI || 'mongodb+srv://Aminos:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster69';
  process.env.JWT_SECRET = process.env.JWT_SECRET || 'mySuperSecretJWTKey123!';
  process.env.ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
  process.env.ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'Adminboss';
}
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');

const adminRoutes = require('./routes/adminRoutes');
const trackRoutes = require('./routes/trackRoutes');

const app = express();

// CORS Configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // List of allowed origins
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://porfolio-pro.onrender.com', // Old static site (keep for backward compatibility)
      'https://porfolio-pro-frontend.onrender.com', // New frontend web service
      'https://porfolio-pro-netlify.app',
      'https://aminos555.github.io'
    ];

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(null, true); // Allow all origins for now, but log blocked ones
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
};

// Middleware
app.use(cors(corsOptions));
app.use(express.json());
app.use(helmet());

// Middleware to extract IP address
app.use((req, res, next) => {
  // Extract the first IP from x-forwarded-for header (original client IP)
  // x-forwarded-for can contain multiple IPs: "client, proxy1, proxy2"
  let clientIP = req.headers['x-forwarded-for'];
  if (clientIP) {
    // Take only the first IP address (original client)
    clientIP = clientIP.split(',')[0].trim();
  } else {
    // Fallback to connection remote address
    clientIP = req.connection.remoteAddress || req.socket.remoteAddress;
  }

  req.visitorIP = clientIP;
  next();
});

// Rate limiter for all requests (can be customized per route)
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Handle preflight requests explicitly
app.options('*', cors(corsOptions));

// Enforce HTTPS in production
if (process.env.NODE_ENV === 'production') {
  app.use((req, res, next) => {
    if (req.headers['x-forwarded-proto'] !== 'https') {
      return res.status(403).json({ message: 'HTTPS required' });
    }
    next();
  });
}

// Routes
app.use('/api/admin', adminRoutes);
app.use('/api/track', trackRoutes);

app.get('/', (req, res) => {
  res.send('Portfolio Backend API');
});

// Ping endpoint for cold start prevention
app.get('/api/ping', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Backend awake',
    timestamp: new Date().toISOString()
  });
});

// MongoDB Connection
const PORT = process.env.PORT || 5000;
console.log('🚀 Starting server...');
console.log('📍 Port:', PORT);

// Debug environment variables
console.log('🔧 Environment Variables Debug:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('MONGO_URI:', process.env.MONGO_URI ? 'Set (length: ' + process.env.MONGO_URI.length + ')' : 'Not set');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'Set' : 'Not set');
console.log('ADMIN_EMAIL:', process.env.ADMIN_EMAIL ? 'Set' : 'Not set');

// Check if MONGO_URI is set
if (!process.env.MONGO_URI) {
  console.error('❌ MONGO_URI environment variable is not set!');
  console.error('Please set MONGO_URI in your Render dashboard environment variables.');
  process.exit(1);
}

console.log('🔗 MongoDB URI: Set');

mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
  .then(() => {
    console.log('✅ MongoDB connected successfully');
    app.listen(PORT, () => {
      console.log(`🎉 Server running on port ${PORT}`);
      console.log(`🌐 Local URL: http://localhost:${PORT}`);
    });
  })
  .catch((err) => {
    console.error('❌ MongoDB connection error:', err);
    process.exit(1);
  });