{"dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.0.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "node-fetch": "^3.3.2"}, "main": "server.js", "scripts": {"start": "node start.js", "start-direct": "node server.js", "dev": "nodemon server.js", "build": "npm install"}, "engines": {"node": ">=18.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}