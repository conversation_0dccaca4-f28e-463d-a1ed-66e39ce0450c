services:
  - type: web
    name: porfolio-pro-backend
    env: node
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: MONGO_URI
        value: mongodb+srv://Aminos:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster69
      - key: JWT_SECRET
        value: mySuperSecretJWTKey123!
      - key: ADMIN_EMAIL
        value: <EMAIL>
      - key: ADMIN_PASSWORD
        value: Adminboss
