// Test deployment script
const fs = require('fs');
const path = require('path');

console.log('🔍 Testing deployment setup...\n');

// Check if required files exist
const requiredFiles = [
  'portfolio-react/public/index.html',
  'portfolio-react/package.json',
  'backend/package.json',
  'backend/server.js',
  'backend/.env'
];

console.log('📁 Checking required files:');
requiredFiles.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`${exists ? '✅' : '❌'} ${file}`);
});

// Check if build directory exists
const buildExists = fs.existsSync('portfolio-react/build');
console.log(`\n📦 Build directory: ${buildExists ? '✅ Exists' : '❌ Missing'}`);

if (buildExists) {
  const buildIndexExists = fs.existsSync('portfolio-react/build/index.html');
  console.log(`📄 Build index.html: ${buildIndexExists ? '✅ Exists' : '❌ Missing'}`);
}

// Check environment variables
console.log('\n🔧 Environment variables:');
const backendEnv = fs.readFileSync('backend/.env', 'utf8');
console.log('Backend .env content:');
console.log(backendEnv);

const frontendEnvExists = fs.existsSync('portfolio-react/.env');
if (frontendEnvExists) {
  const frontendEnv = fs.readFileSync('portfolio-react/.env', 'utf8');
  console.log('\nFrontend .env content:');
  console.log(frontendEnv);
}

console.log('\n🚀 Deployment test complete!');
