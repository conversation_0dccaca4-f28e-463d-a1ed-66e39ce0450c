# Portfolio Pro - Deployment Guide

## Issues Fixed

### 1. Backend Deployment Issues
- ✅ **MongoDB Connection**: Added proper MONGO_URI environment variable
- ✅ **Environment Variables**: Set all required env vars in render.yaml
- ✅ **Build Configuration**: Simplified build commands

### 2. Frontend Deployment Issues  
- ✅ **Static Site Configuration**: Changed from Node.js to static site deployment
- ✅ **Build Path**: Set correct staticPublishPath to `./build`
- ✅ **API URL**: Set production API URL environment variable

## Deployment Instructions

### Backend Deployment (Render)

1. **Create New Web Service**:
   - Go to [Render Dashboard](https://dashboard.render.com)
   - Click "New" → "Web Service"
   - Connect your GitHub repository: `https://github.com/aminos555/Porfolio-Pro`

2. **Configure Backend Service**:
   - **Name**: `porfolio-pro-backend`
   - **Environment**: `Node`
   - **Root Directory**: `backend`
   - **Build Command**: `npm install`
   - **Start Command**: `npm start`

3. **Environment Variables** (Add these in Render dashboard):
   ```
   NODE_ENV=production
   MONGO_URI=mongodb+srv://Aminos:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster69
   JWT_SECRET=mySuperSecretJWTKey123!
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=Adminboss
   ```

### Frontend Deployment (Render)

1. **Create New Static Site**:
   - Go to [Render Dashboard](https://dashboard.render.com)
   - Click "New" → "Static Site"
   - Connect your GitHub repository: `https://github.com/aminos555/Porfolio-Pro`

2. **Configure Frontend Service**:
   - **Name**: `porfolio-pro-frontend`
   - **Root Directory**: `portfolio-react`
   - **Build Command**: `npm install && npm run build`
   - **Publish Directory**: `build`

3. **Environment Variables** (Add these in Render dashboard):
   ```
   NODE_ENV=production
   REACT_APP_API_URL=https://porfolio-pro-backend.onrender.com
   ```

## Alternative: Use render.yaml Files

You can also use the render.yaml files I've created:

### For Backend:
- Use `backend/render.yaml` when creating the backend service
- This will automatically configure all settings

### For Frontend:
- Use `portfolio-react/render.yaml` when creating the frontend service
- This will automatically configure all settings

## Post-Deployment Steps

1. **Test Backend**: Visit your backend URL (e.g., `https://porfolio-pro-backend.onrender.com/api/health`)
2. **Test Frontend**: Visit your frontend URL (e.g., `https://porfolio-pro-frontend.onrender.com`)
3. **Update API URLs**: Make sure frontend is pointing to the correct backend URL

## Troubleshooting

### Backend Issues:
- Check MongoDB connection string is correct
- Verify all environment variables are set
- Check logs for specific error messages

### Frontend Issues:
- Ensure `index.html` exists in `portfolio-react/public/`
- Verify build command produces files in `build/` directory
- Check that `REACT_APP_API_URL` points to deployed backend

## Files Updated:
- ✅ `render.yaml` - Main deployment configuration
- ✅ `backend/render.yaml` - Backend-specific configuration  
- ✅ `portfolio-react/render.yaml` - Frontend-specific configuration
- ✅ `backend/.env` - Backend environment variables
- ✅ `portfolio-react/.env` - Frontend environment variables

The deployment should now work correctly! 🚀
