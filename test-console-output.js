// Test script to verify console output in different environments
console.log('🧪 Testing console output behavior...\n');

// Simulate development environment
process.env.NODE_ENV = 'development';
console.log('📍 Testing in DEVELOPMENT mode:');

// Import the debug utilities
const { debugEnvironment, testAPIConnection, runEnvironmentDiagnostics } = require('./portfolio-react/src/utils/debugEnvironment.js');

// Test development mode
console.log('\n--- Development Mode Test ---');
runEnvironmentDiagnostics().then(() => {
  console.log('\n--- Switching to Production Mode ---');
  
  // Simulate production environment
  process.env.NODE_ENV = 'production';
  console.log('📍 Testing in PRODUCTION mode:');
  
  // Test production mode (should be silent)
  return runEnvironmentDiagnostics();
}).then(() => {
  console.log('\n✅ Console output test completed!');
  console.log('In production, you should only see backend wakeup messages.');
}).catch(error => {
  console.error('❌ Test failed:', error.message);
});
