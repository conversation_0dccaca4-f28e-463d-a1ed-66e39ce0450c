#!/usr/bin/env node

// Production startup script with environment variable validation
console.log('🚀 Starting Portfolio Pro Backend...');

// Set production environment variables if not already set
if (process.env.NODE_ENV === 'production') {
  console.log('🔧 Production mode detected, setting up environment...');
  
  // Set environment variables if not already set by <PERSON><PERSON>
  if (!process.env.MONGO_URI) {
    process.env.MONGO_URI = 'mongodb+srv://Aminos:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster69';
    console.log('✅ MONGO_URI set from fallback');
  }
  
  if (!process.env.JWT_SECRET) {
    process.env.JWT_SECRET = 'mySuperSecretJWTKey123!';
    console.log('✅ JWT_SECRET set from fallback');
  }
  
  if (!process.env.ADMIN_EMAIL) {
    process.env.ADMIN_EMAIL = '<EMAIL>';
    console.log('✅ ADMIN_EMAIL set from fallback');
  }
  
  if (!process.env.ADMIN_PASSWORD) {
    process.env.ADMIN_PASSWORD = 'Adminboss';
    console.log('✅ ADMIN_PASSWORD set from fallback');
  }
}

// Now start the actual server
require('./server.js');
